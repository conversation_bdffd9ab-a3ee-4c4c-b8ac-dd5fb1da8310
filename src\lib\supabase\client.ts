/**
 * Supabase Client for Real-time Features
 * Handles real-time messaging and notifications only
 * Primary data operations remain with MySQL
 */

import { createClient, SupabaseClient, RealtimeChannel } from '@supabase/supabase-js';
import { supabaseConfig, SUPABASE_URL, SUPABASE_ANON_KEY, SUPABASE_SERVICE_ROLE_KEY } from './config';

// Types for real-time data
export interface RealtimeMessage {
  id: string;
  sender_id: string;
  receiver_id: string;
  content: string;
  message_type: 'text' | 'image' | 'file';
  created_at: string;
  mysql_id?: string; // Reference to MySQL record
}

export interface RealtimeNotification {
  id: string;
  recipient_id: string;
  type: string;
  data: Record<string, any>;
  created_at: string;
  read_status: boolean;
  mysql_id?: string; // Reference to MySQL record
}

export interface UserPresence {
  user_id: string;
  status: 'online' | 'offline' | 'away';
  last_seen: string;
  typing_in?: string; // conversation_id if typing
}

// Client for public operations (frontend) - only create if config is valid
export const supabaseClient: SupabaseClient = SUPABASE_URL && SUPABASE_ANON_KEY
  ? createClient(
      SUPABASE_URL,
      SUPABASE_ANON_KEY,
      {
        auth: supabaseConfig.auth,
        realtime: {
          params: {
            eventsPerSecond: 10, // Rate limiting
          },
        },
      }
    )
  : null as any; // Fallback for when Supabase is not configured

// Admin client for server-side operations - only create if config is valid
export const supabaseAdmin: SupabaseClient = SUPABASE_URL && SUPABASE_SERVICE_ROLE_KEY
  ? createClient(
      SUPABASE_URL,
      SUPABASE_SERVICE_ROLE_KEY,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false,
        },
      }
    )
  : null as any; // Fallback for when Supabase is not configured

// Connection management with performance optimization
class SupabaseConnectionManager {
  private channels: Map<string, RealtimeChannel> = new Map();
  private reconnectAttempts: number = 0;
  private maxReconnectAttempts: number = supabaseConfig.realtime.maxReconnectAttempts;
  private connectionPool: Map<string, RealtimeChannel> = new Map();
  private maxPoolSize: number = 10;
  private channelUsageCount: Map<string, number> = new Map();

  // Subscribe to real-time messages for a user (with connection pooling)
  subscribeToMessages(userId: string, callback: (message: RealtimeMessage) => void): RealtimeChannel {
    const channelName = `messages:${userId}`;

    // Check if channel already exists
    if (this.channels.has(channelName)) {
      const existingChannel = this.channels.get(channelName)!;
      this.incrementChannelUsage(channelName);
      return existingChannel;
    }

    // Check connection pool first
    const pooledChannel = this.getFromPool(channelName);
    if (pooledChannel) {
      this.channels.set(channelName, pooledChannel);
      this.incrementChannelUsage(channelName);
      return pooledChannel;
    }

    const channel = supabaseClient
      .channel(channelName)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: supabaseConfig.tables.messages,
          filter: `receiver_id=eq.${userId}`,
        },
        (payload) => {
          callback(payload.new as RealtimeMessage);
        }
      )
      .subscribe((status) => {
        console.log(`Messages subscription status for ${userId}:`, status);
        if (status === 'SUBSCRIBED') {
          this.reconnectAttempts = 0;
        }
      });

    this.channels.set(channelName, channel);
    return channel;
  }

  // Subscribe to real-time notifications for a user
  subscribeToNotifications(userId: string, callback: (notification: RealtimeNotification) => void): RealtimeChannel {
    const channelName = `notifications:${userId}`;
    
    if (this.channels.has(channelName)) {
      return this.channels.get(channelName)!;
    }

    const channel = supabaseClient
      .channel(channelName)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: supabaseConfig.tables.notifications,
          filter: `recipient_id=eq.${userId}`,
        },
        (payload) => {
          callback(payload.new as RealtimeNotification);
        }
      )
      .subscribe((status) => {
        console.log(`Notifications subscription status for ${userId}:`, status);
      });

    this.channels.set(channelName, channel);
    return channel;
  }

  // Subscribe to user presence
  subscribeToPresence(userId: string, callback: (presence: UserPresence[]) => void): RealtimeChannel {
    const channelName = `presence:global`;
    
    if (this.channels.has(channelName)) {
      return this.channels.get(channelName)!;
    }

    const channel = supabaseClient
      .channel(channelName)
      .on('presence', { event: 'sync' }, () => {
        const state = channel.presenceState();
        const presenceList: UserPresence[] = Object.values(state).flat() as UserPresence[];
        callback(presenceList);
      })
      .subscribe();

    this.channels.set(channelName, channel);
    return channel;
  }

  // Unsubscribe from a channel
  unsubscribe(channelName: string): void {
    const channel = this.channels.get(channelName);
    if (channel) {
      supabaseClient.removeChannel(channel);
      this.channels.delete(channelName);
    }
  }

  // Unsubscribe from all channels
  unsubscribeAll(): void {
    this.channels.forEach((channel, channelName) => {
      supabaseClient.removeChannel(channel);
    });
    this.channels.clear();
  }

  // Connection pool management
  private getFromPool(channelName: string): RealtimeChannel | null {
    return this.connectionPool.get(channelName) || null;
  }

  private addToPool(channelName: string, channel: RealtimeChannel): void {
    if (this.connectionPool.size >= this.maxPoolSize) {
      // Remove least used channel
      const leastUsed = this.findLeastUsedChannel();
      if (leastUsed) {
        this.connectionPool.delete(leastUsed);
        this.channelUsageCount.delete(leastUsed);
      }
    }

    this.connectionPool.set(channelName, channel);
    this.channelUsageCount.set(channelName, 0);
  }

  private findLeastUsedChannel(): string | null {
    let leastUsed: string | null = null;
    let minUsage = Infinity;

    for (const [channelName, usage] of this.channelUsageCount.entries()) {
      if (usage < minUsage) {
        minUsage = usage;
        leastUsed = channelName;
      }
    }

    return leastUsed;
  }

  private incrementChannelUsage(channelName: string): void {
    const current = this.channelUsageCount.get(channelName) || 0;
    this.channelUsageCount.set(channelName, current + 1);
  }

  // Get connection status
  getConnectionStatus(): string {
    return supabaseClient.realtime.connection?.readyState === 1 ? 'connected' : 'disconnected';
  }

  // Get connection pool statistics
  getPoolStats(): { poolSize: number; activeChannels: number; maxPoolSize: number } {
    return {
      poolSize: this.connectionPool.size,
      activeChannels: this.channels.size,
      maxPoolSize: this.maxPoolSize,
    };
  }
}

// Export singleton instance
export const connectionManager = new SupabaseConnectionManager();

// Utility functions
export const isRealtimeEnabled = (): boolean => {
  return supabaseConfig.realtime.enabled && !!SUPABASE_URL && !!SUPABASE_ANON_KEY && !!supabaseClient;
};

export const getSupabaseStatus = () => {
  return {
    enabled: isRealtimeEnabled(),
    connected: connectionManager.getConnectionStatus() === 'connected',
    channels: connectionManager['channels'].size,
  };
};
