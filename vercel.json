{"framework": "nextjs", "buildCommand": "npm run build", "devCommand": "npm run dev", "installCommand": "npm install", "outputDirectory": ".next", "functions": {"app/api/**/*.ts": {"maxDuration": 30}}, "env": {"NEXT_PUBLIC_APP_URL": "@next_public_app_url", "NEXT_PUBLIC_SUPABASE_URL": "@next_public_supabase_url", "NEXT_PUBLIC_SUPABASE_ANON_KEY": "@next_public_supabase_anon_key", "SUPABASE_SERVICE_ROLE_KEY": "@supabase_service_role_key", "ENABLE_REALTIME": "@enable_realtime", "REALTIME_PROVIDER": "@realtime_provider"}, "headers": [{"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}]}], "rewrites": [{"source": "/api/realtime/:path*", "destination": "/api/realtime/:path*"}], "crons": [{"path": "/api/cron/cleanup-realtime", "schedule": "0 2 * * *"}]}