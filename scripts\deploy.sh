#!/bin/bash

# Production Deployment Script for Hybrid Real-time System
# This script prepares and deploys the application to Vercel

set -e  # Exit on any error

echo "🚀 Starting Production Deployment..."
echo "=================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if required tools are installed
check_dependencies() {
    print_status "Checking dependencies..."
    
    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed"
        exit 1
    fi
    
    if ! command -v npm &> /dev/null; then
        print_error "npm is not installed"
        exit 1
    fi
    
    if ! command -v vercel &> /dev/null; then
        print_warning "Vercel CLI is not installed. Installing..."
        npm install -g vercel
    fi
    
    print_success "All dependencies are available"
}

# Check environment variables
check_environment() {
    print_status "Checking environment variables..."
    
    if [ ! -f ".env" ]; then
        print_error ".env file not found"
        print_status "Please create .env file with required variables"
        print_status "See .env.example for reference"
        exit 1
    fi
    
    # Check for required variables
    required_vars=(
        "NEXT_PUBLIC_SUPABASE_URL"
        "NEXT_PUBLIC_SUPABASE_ANON_KEY"
        "SUPABASE_SERVICE_ROLE_KEY"
        "ENABLE_REALTIME"
    )
    
    missing_vars=()
    
    for var in "${required_vars[@]}"; do
        if ! grep -q "^${var}=" .env; then
            missing_vars+=("$var")
        fi
    done
    
    if [ ${#missing_vars[@]} -ne 0 ]; then
        print_error "Missing required environment variables:"
        for var in "${missing_vars[@]}"; do
            echo "  - $var"
        done
        exit 1
    fi
    
    print_success "Environment variables are configured"
}

# Install dependencies
install_dependencies() {
    print_status "Installing dependencies..."
    npm ci
    print_success "Dependencies installed"
}

# Run tests
run_tests() {
    print_status "Running system tests..."
    
    # Test Supabase connection
    print_status "Testing Supabase connection..."
    if npm run test-supabase; then
        print_success "Supabase connection test passed"
    else
        print_error "Supabase connection test failed"
        exit 1
    fi
    
    # Test hybrid sync
    print_status "Testing hybrid sync..."
    if npm run test-hybrid-sync; then
        print_success "Hybrid sync test passed"
    else
        print_warning "Hybrid sync test failed (may work in production)"
    fi
    
    # Test frontend integration
    print_status "Testing frontend integration..."
    if npm run test-frontend; then
        print_success "Frontend integration test passed"
    else
        print_warning "Frontend integration test failed (may work in production)"
    fi
    
    print_success "Core tests completed"
}

# Build the application
build_application() {
    print_status "Building application..."
    
    if npm run build; then
        print_success "Application built successfully"
    else
        print_error "Build failed"
        exit 1
    fi
}

# Deploy to Vercel
deploy_to_vercel() {
    print_status "Deploying to Vercel..."
    
    # Check if user is logged in to Vercel
    if ! vercel whoami &> /dev/null; then
        print_status "Please log in to Vercel..."
        vercel login
    fi
    
    # Deploy to production
    print_status "Deploying to production..."
    if vercel --prod; then
        print_success "Deployment successful!"
    else
        print_error "Deployment failed"
        exit 1
    fi
}

# Post-deployment verification
verify_deployment() {
    print_status "Verifying deployment..."
    
    # Get deployment URL
    DEPLOYMENT_URL=$(vercel ls --scope=$(vercel whoami) | grep "$(basename $(pwd))" | head -1 | awk '{print $2}')
    
    if [ -z "$DEPLOYMENT_URL" ]; then
        print_warning "Could not determine deployment URL"
        return
    fi
    
    print_status "Deployment URL: https://$DEPLOYMENT_URL"
    
    # Test health endpoint
    print_status "Testing health endpoint..."
    if curl -f -s "https://$DEPLOYMENT_URL/api/health" > /dev/null; then
        print_success "Health endpoint is responding"
    else
        print_warning "Health endpoint is not responding (may take a moment to start)"
    fi
    
    # Test real-time endpoint
    print_status "Testing real-time endpoint..."
    if curl -f -s "https://$DEPLOYMENT_URL/api/health/realtime" > /dev/null; then
        print_success "Real-time endpoint is responding"
    else
        print_warning "Real-time endpoint is not responding"
    fi
}

# Main deployment process
main() {
    echo "🚀 Hybrid MySQL + Supabase Real-time System Deployment"
    echo "======================================================"
    echo ""
    
    check_dependencies
    echo ""
    
    check_environment
    echo ""
    
    install_dependencies
    echo ""
    
    run_tests
    echo ""
    
    build_application
    echo ""
    
    deploy_to_vercel
    echo ""
    
    verify_deployment
    echo ""
    
    print_success "🎉 Deployment completed successfully!"
    echo ""
    echo "📋 Next Steps:"
    echo "1. Configure production environment variables in Vercel dashboard"
    echo "2. Set up Supabase real-time tables using supabase-setup.sql"
    echo "3. Test real-time features in production"
    echo "4. Monitor system performance and errors"
    echo ""
    echo "📖 Documentation:"
    echo "- Production Setup: PRODUCTION_SETUP.md"
    echo "- System Overview: REALTIME_SYSTEM.md"
    echo ""
    echo "🔍 Monitoring:"
    echo "- Health: https://$DEPLOYMENT_URL/api/health"
    echo "- Real-time: https://$DEPLOYMENT_URL/api/health/realtime"
    echo "- Metrics: https://$DEPLOYMENT_URL/api/metrics/performance"
}

# Handle script arguments
case "${1:-}" in
    --skip-tests)
        print_warning "Skipping tests as requested"
        run_tests() { print_warning "Tests skipped"; }
        ;;
    --help)
        echo "Usage: $0 [--skip-tests] [--help]"
        echo ""
        echo "Options:"
        echo "  --skip-tests    Skip running tests before deployment"
        echo "  --help          Show this help message"
        exit 0
        ;;
esac

# Run main deployment process
main
