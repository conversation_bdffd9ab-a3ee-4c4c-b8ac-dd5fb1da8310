/**
 * Connection Recovery Service
 * Handles automatic reconnection and fallback mechanisms
 */

import { supabaseClient, connectionManager, isRealtimeEnabled } from './client';
import { errorHandler, withRetry } from './errorHandler';
import { toast } from 'react-hot-toast';

export interface ConnectionState {
  isConnected: boolean;
  isRecovering: boolean;
  lastConnected: Date | null;
  reconnectAttempts: number;
  maxReconnectAttempts: number;
  fallbackMode: boolean;
}

export class ConnectionRecoveryService {
  private static instance: ConnectionRecoveryService;
  private state: ConnectionState = {
    isConnected: false,
    isRecovering: false,
    lastConnected: null,
    reconnectAttempts: 0,
    maxReconnectAttempts: 5,
    fallbackMode: false,
  };

  private reconnectTimer: NodeJS.Timeout | null = null;
  private healthCheckTimer: NodeJS.Timeout | null = null;
  private listeners: Array<(state: ConnectionState) => void> = [];

  static getInstance(): ConnectionRecoveryService {
    if (!ConnectionRecoveryService.instance) {
      ConnectionRecoveryService.instance = new ConnectionRecoveryService();
    }
    return ConnectionRecoveryService.instance;
  }

  /**
   * Initialize connection recovery
   */
  initialize(): void {
    if (!isRealtimeEnabled()) {
      console.log('🔌 Connection recovery disabled - real-time features not enabled');
      return;
    }

    console.log('🔌 Initializing connection recovery service...');
    
    // Start health monitoring
    this.startHealthCheck();
    
    // Listen for connection events
    this.setupConnectionListeners();
    
    // Initial connection check
    this.checkConnection();
  }

  /**
   * Setup connection event listeners
   */
  private setupConnectionListeners(): void {
    if (typeof window !== 'undefined') {
      // Browser online/offline events
      window.addEventListener('online', this.handleOnline.bind(this));
      window.addEventListener('offline', this.handleOffline.bind(this));
      
      // Page visibility changes
      document.addEventListener('visibilitychange', this.handleVisibilityChange.bind(this));
    }
  }

  /**
   * Handle browser online event
   */
  private handleOnline(): void {
    console.log('🌐 Browser is online - attempting reconnection...');
    this.attemptReconnection();
  }

  /**
   * Handle browser offline event
   */
  private handleOffline(): void {
    console.log('🌐 Browser is offline - entering fallback mode');
    this.enterFallbackMode();
  }

  /**
   * Handle page visibility change
   */
  private handleVisibilityChange(): void {
    if (document.visibilityState === 'visible') {
      console.log('👁️ Page is visible - checking connection...');
      this.checkConnection();
    }
  }

  /**
   * Start periodic health checks
   */
  private startHealthCheck(): void {
    this.healthCheckTimer = setInterval(() => {
      this.checkConnection();
    }, 30000); // Check every 30 seconds
  }

  /**
   * Check current connection status
   */
  private async checkConnection(): Promise<void> {
    try {
      const connectionStatus = connectionManager.getConnectionStatus();
      const isConnected = connectionStatus === 'connected';
      
      if (isConnected !== this.state.isConnected) {
        this.updateConnectionState({
          isConnected,
          lastConnected: isConnected ? new Date() : this.state.lastConnected,
          reconnectAttempts: isConnected ? 0 : this.state.reconnectAttempts,
          fallbackMode: !isConnected && this.state.reconnectAttempts >= this.state.maxReconnectAttempts,
        });

        if (isConnected) {
          this.handleConnectionRestored();
        } else {
          this.handleConnectionLost();
        }
      }
    } catch (error) {
      errorHandler.handleError(error, {
        operation: 'connection_health_check',
        timestamp: new Date(),
      });
    }
  }

  /**
   * Handle connection restored
   */
  private handleConnectionRestored(): void {
    console.log('✅ Real-time connection restored');
    
    if (this.state.fallbackMode) {
      toast.success('Real-time connection restored!', {
        duration: 3000,
        position: 'top-right',
      });
    }

    // Clear any reconnection timers
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }

    this.updateConnectionState({
      isRecovering: false,
      fallbackMode: false,
    });
  }

  /**
   * Handle connection lost
   */
  private handleConnectionLost(): void {
    console.log('❌ Real-time connection lost');
    
    if (!this.state.isRecovering) {
      this.attemptReconnection();
    }
  }

  /**
   * Attempt to reconnect
   */
  private async attemptReconnection(): Promise<void> {
    if (this.state.isRecovering || this.state.reconnectAttempts >= this.state.maxReconnectAttempts) {
      return;
    }

    this.updateConnectionState({
      isRecovering: true,
      reconnectAttempts: this.state.reconnectAttempts + 1,
    });

    console.log(`🔄 Attempting reconnection (${this.state.reconnectAttempts}/${this.state.maxReconnectAttempts})...`);

    try {
      // Try to reconnect with retry logic
      await withRetry(
        async () => {
          // Force reconnection by removing and re-adding channels
          connectionManager.unsubscribeAll();
          
          // Wait a bit before reconnecting
          await new Promise(resolve => setTimeout(resolve, 1000));
          
          // Check if connection is restored
          const status = connectionManager.getConnectionStatus();
          if (status !== 'connected') {
            throw new Error('Connection not restored');
          }
          
          return true;
        },
        {
          operation: 'reconnection_attempt',
          timestamp: new Date(),
          retryCount: this.state.reconnectAttempts,
        },
        {
          maxRetries: 2,
          baseDelay: 2000,
          maxDelay: 8000,
        }
      );

      // Check connection after retry
      await this.checkConnection();
      
    } catch (error) {
      console.error('❌ Reconnection failed:', error);
      
      if (this.state.reconnectAttempts >= this.state.maxReconnectAttempts) {
        this.enterFallbackMode();
      } else {
        // Schedule next reconnection attempt
        this.scheduleReconnection();
      }
    } finally {
      this.updateConnectionState({
        isRecovering: false,
      });
    }
  }

  /**
   * Schedule next reconnection attempt
   */
  private scheduleReconnection(): void {
    const delay = Math.min(5000 * Math.pow(2, this.state.reconnectAttempts - 1), 30000);
    
    console.log(`⏰ Scheduling next reconnection in ${delay}ms...`);
    
    this.reconnectTimer = setTimeout(() => {
      this.attemptReconnection();
    }, delay);
  }

  /**
   * Enter fallback mode
   */
  private enterFallbackMode(): void {
    console.log('🔄 Entering fallback mode - real-time features disabled');
    
    this.updateConnectionState({
      fallbackMode: true,
      isRecovering: false,
    });

    toast.error('Real-time features temporarily unavailable. Using fallback mode.', {
      duration: 5000,
      position: 'top-right',
    });
  }

  /**
   * Exit fallback mode and retry connection
   */
  exitFallbackMode(): void {
    console.log('🔄 Exiting fallback mode - retrying connection...');
    
    this.updateConnectionState({
      fallbackMode: false,
      reconnectAttempts: 0,
    });

    this.attemptReconnection();
  }

  /**
   * Update connection state and notify listeners
   */
  private updateConnectionState(updates: Partial<ConnectionState>): void {
    this.state = { ...this.state, ...updates };
    this.notifyListeners();
  }

  /**
   * Add state change listener
   */
  addListener(listener: (state: ConnectionState) => void): () => void {
    this.listeners.push(listener);
    
    // Return unsubscribe function
    return () => {
      const index = this.listeners.indexOf(listener);
      if (index > -1) {
        this.listeners.splice(index, 1);
      }
    };
  }

  /**
   * Notify all listeners of state changes
   */
  private notifyListeners(): void {
    this.listeners.forEach(listener => {
      try {
        listener(this.state);
      } catch (error) {
        console.error('Error in connection state listener:', error);
      }
    });
  }

  /**
   * Get current connection state
   */
  getState(): ConnectionState {
    return { ...this.state };
  }

  /**
   * Force reconnection
   */
  forceReconnection(): void {
    console.log('🔄 Forcing reconnection...');
    
    this.updateConnectionState({
      reconnectAttempts: 0,
      fallbackMode: false,
    });

    this.attemptReconnection();
  }

  /**
   * Cleanup resources
   */
  cleanup(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }

    if (this.healthCheckTimer) {
      clearInterval(this.healthCheckTimer);
      this.healthCheckTimer = null;
    }

    if (typeof window !== 'undefined') {
      window.removeEventListener('online', this.handleOnline.bind(this));
      window.removeEventListener('offline', this.handleOffline.bind(this));
      document.removeEventListener('visibilitychange', this.handleVisibilityChange.bind(this));
    }

    this.listeners = [];
  }
}

// Export singleton instance
export const connectionRecovery = ConnectionRecoveryService.getInstance();
