/**
 * Fallback Service
 * Provides alternative methods when real-time features are unavailable
 */

import { errorHandler } from './errorHandler';

export interface FallbackConfig {
  pollingInterval: number;
  maxPollingInterval: number;
  enablePolling: boolean;
  enableLocalStorage: boolean;
  enableServiceWorker: boolean;
}

export class FallbackService {
  private static instance: FallbackService;
  private config: FallbackConfig = {
    pollingInterval: 5000, // 5 seconds
    maxPollingInterval: 30000, // 30 seconds
    enablePolling: true,
    enableLocalStorage: true,
    enableServiceWorker: false, // Disabled by default
  };

  private pollingTimers: Map<string, NodeJS.Timeout> = new Map();
  private pollingCallbacks: Map<string, () => Promise<void>> = new Map();
  private isInFallbackMode = false;

  static getInstance(): FallbackService {
    if (!FallbackService.instance) {
      FallbackService.instance = new FallbackService();
    }
    return FallbackService.instance;
  }

  /**
   * Enter fallback mode
   */
  enterFallbackMode(): void {
    if (this.isInFallbackMode) return;

    console.log('🔄 Entering fallback mode...');
    this.isInFallbackMode = true;

    // Start polling for critical data
    this.startPolling();

    // Setup local storage fallbacks
    if (this.config.enableLocalStorage) {
      this.setupLocalStorageFallbacks();
    }

    // Setup service worker fallbacks (if enabled)
    if (this.config.enableServiceWorker) {
      this.setupServiceWorkerFallbacks();
    }
  }

  /**
   * Exit fallback mode
   */
  exitFallbackMode(): void {
    if (!this.isInFallbackMode) return;

    console.log('✅ Exiting fallback mode...');
    this.isInFallbackMode = false;

    // Stop all polling
    this.stopAllPolling();

    // Clear local storage fallbacks
    this.clearLocalStorageFallbacks();
  }

  /**
   * Start polling for data updates
   */
  private startPolling(): void {
    // Poll for new messages
    this.startPollingFor('messages', async () => {
      await this.pollMessages();
    });

    // Poll for new notifications
    this.startPollingFor('notifications', async () => {
      await this.pollNotifications();
    });

    // Poll for user presence (less frequently)
    this.startPollingFor('presence', async () => {
      await this.pollPresence();
    }, this.config.pollingInterval * 3);
  }

  /**
   * Start polling for a specific data type
   */
  private startPollingFor(key: string, callback: () => Promise<void>, interval?: number): void {
    const pollInterval = interval || this.config.pollingInterval;

    const poll = async () => {
      try {
        await callback();
      } catch (error) {
        errorHandler.handleError(error, {
          operation: `fallback_polling_${key}`,
          timestamp: new Date(),
        });

        // Increase polling interval on error (exponential backoff)
        const currentInterval = pollInterval * Math.min(2, Math.pow(1.5, 3));
        const newInterval = Math.min(currentInterval, this.config.maxPollingInterval);
        
        // Restart with new interval
        this.stopPollingFor(key);
        setTimeout(() => {
          this.startPollingFor(key, callback, newInterval);
        }, newInterval);
        return;
      }

      // Schedule next poll
      const timer = setTimeout(poll, pollInterval);
      this.pollingTimers.set(key, timer);
    };

    // Start polling
    poll();
    this.pollingCallbacks.set(key, callback);
  }

  /**
   * Stop polling for a specific data type
   */
  private stopPollingFor(key: string): void {
    const timer = this.pollingTimers.get(key);
    if (timer) {
      clearTimeout(timer);
      this.pollingTimers.delete(key);
    }
    this.pollingCallbacks.delete(key);
  }

  /**
   * Stop all polling
   */
  private stopAllPolling(): void {
    this.pollingTimers.forEach((timer, key) => {
      clearTimeout(timer);
    });
    this.pollingTimers.clear();
    this.pollingCallbacks.clear();
  }

  /**
   * Poll for new messages
   */
  private async pollMessages(): Promise<void> {
    try {
      // Get last message timestamp from local storage
      const lastMessageTime = localStorage.getItem('fallback_last_message_time');
      const since = lastMessageTime ? new Date(lastMessageTime) : new Date(Date.now() - 60000); // Last minute

      // Fetch new messages from API
      const response = await fetch(`/api/messages?since=${since.toISOString()}`, {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' },
      });

      if (response.ok) {
        const data = await response.json();
        
        if (data.messages && data.messages.length > 0) {
          // Process new messages
          this.processNewMessages(data.messages);
          
          // Update last message time
          const latestMessage = data.messages[data.messages.length - 1];
          localStorage.setItem('fallback_last_message_time', latestMessage.createdAt);
        }
      }
    } catch (error) {
      throw new Error(`Message polling failed: ${error}`);
    }
  }

  /**
   * Poll for new notifications
   */
  private async pollNotifications(): Promise<void> {
    try {
      // Get last notification timestamp from local storage
      const lastNotificationTime = localStorage.getItem('fallback_last_notification_time');
      const since = lastNotificationTime ? new Date(lastNotificationTime) : new Date(Date.now() - 60000);

      // Fetch new notifications from API
      const response = await fetch(`/api/notifications?since=${since.toISOString()}`, {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' },
      });

      if (response.ok) {
        const data = await response.json();
        
        if (data.notifications && data.notifications.length > 0) {
          // Process new notifications
          this.processNewNotifications(data.notifications);
          
          // Update last notification time
          const latestNotification = data.notifications[data.notifications.length - 1];
          localStorage.setItem('fallback_last_notification_time', latestNotification.createdAt);
        }
      }
    } catch (error) {
      throw new Error(`Notification polling failed: ${error}`);
    }
  }

  /**
   * Poll for user presence updates
   */
  private async pollPresence(): Promise<void> {
    try {
      // Fetch presence data from API
      const response = await fetch('/api/users/presence', {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' },
      });

      if (response.ok) {
        const data = await response.json();
        this.processPresenceUpdates(data.presence || []);
      }
    } catch (error) {
      throw new Error(`Presence polling failed: ${error}`);
    }
  }

  /**
   * Process new messages in fallback mode
   */
  private processNewMessages(messages: any[]): void {
    messages.forEach(message => {
      // Dispatch custom event for message components to listen
      if (typeof window !== 'undefined') {
        window.dispatchEvent(new CustomEvent('fallback-message', {
          detail: { message }
        }));
      }
    });

    console.log(`📨 Processed ${messages.length} new messages via fallback`);
  }

  /**
   * Process new notifications in fallback mode
   */
  private processNewNotifications(notifications: any[]): void {
    notifications.forEach(notification => {
      // Dispatch custom event for notification components to listen
      if (typeof window !== 'undefined') {
        window.dispatchEvent(new CustomEvent('fallback-notification', {
          detail: { notification }
        }));
      }
    });

    console.log(`🔔 Processed ${notifications.length} new notifications via fallback`);
  }

  /**
   * Process presence updates in fallback mode
   */
  private processPresenceUpdates(presenceList: any[]): void {
    // Store presence data in local storage
    if (this.config.enableLocalStorage) {
      localStorage.setItem('fallback_presence_data', JSON.stringify(presenceList));
    }

    // Dispatch custom event
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('fallback-presence', {
        detail: { presence: presenceList }
      }));
    }
  }

  /**
   * Setup local storage fallbacks
   */
  private setupLocalStorageFallbacks(): void {
    if (typeof window === 'undefined') return;

    // Store fallback mode flag
    localStorage.setItem('fallback_mode_active', 'true');
    localStorage.setItem('fallback_mode_started', new Date().toISOString());
  }

  /**
   * Clear local storage fallbacks
   */
  private clearLocalStorageFallbacks(): void {
    if (typeof window === 'undefined') return;

    // Clear fallback mode flags
    localStorage.removeItem('fallback_mode_active');
    localStorage.removeItem('fallback_mode_started');
    
    // Keep data for a while in case of quick reconnection
    setTimeout(() => {
      localStorage.removeItem('fallback_last_message_time');
      localStorage.removeItem('fallback_last_notification_time');
      localStorage.removeItem('fallback_presence_data');
    }, 60000); // Clear after 1 minute
  }

  /**
   * Setup service worker fallbacks (future enhancement)
   */
  private setupServiceWorkerFallbacks(): void {
    // This would setup service worker for background sync
    // when the app is not in focus
    console.log('📱 Service worker fallbacks not implemented yet');
  }

  /**
   * Get fallback status
   */
  getStatus(): { isActive: boolean; pollingCount: number; config: FallbackConfig } {
    return {
      isActive: this.isInFallbackMode,
      pollingCount: this.pollingTimers.size,
      config: this.config,
    };
  }

  /**
   * Update fallback configuration
   */
  updateConfig(updates: Partial<FallbackConfig>): void {
    this.config = { ...this.config, ...updates };
    
    // Restart polling with new config if in fallback mode
    if (this.isInFallbackMode) {
      this.stopAllPolling();
      this.startPolling();
    }
  }

  /**
   * Manually trigger polling for testing
   */
  async triggerPolling(type: 'messages' | 'notifications' | 'presence'): Promise<void> {
    const callback = this.pollingCallbacks.get(type);
    if (callback) {
      await callback();
    }
  }
}

// Export singleton instance
export const fallbackService = FallbackService.getInstance();
