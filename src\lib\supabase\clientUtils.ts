/**
 * Client-safe utilities for Supabase operations
 * These functions make API calls instead of directly importing server-side code
 */

/**
 * Update user presence status via API call
 */
export async function updatePresenceClient(
  status: 'online' | 'offline' | 'away',
  typingIn?: string
): Promise<void> {
  try {
    const response = await fetch('/api/presence', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        status,
        typingIn,
      }),
    });

    if (!response.ok) {
      throw new Error(`Failed to update presence: ${response.statusText}`);
    }
  } catch (error) {
    console.error('Error updating presence:', error);
    throw error;
  }
}
