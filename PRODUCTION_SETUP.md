# Production Deployment Guide

## 🚀 Hybrid MySQL + Supabase Real-time System

This guide covers the production deployment of the hybrid real-time system that uses MySQL as the primary database and Supabase for real-time features.

## 📋 Prerequisites

- [x] MySQL database (primary data storage)
- [x] Supabase project (real-time features)
- [x] Vercel account (deployment platform)
- [x] Domain name (optional)

## 🔧 Environment Variables Setup

### Required Environment Variables

Add these environment variables in your Vercel project settings:

```bash
# Application Configuration
NEXT_PUBLIC_APP_URL=https://your-domain.vercel.app

# Database Configuration (MySQL - Primary)
DATABASE_URL=mysql://username:password@host:port/database
DIRECT_URL=mysql://username:password@host:port/database

# Supabase Configuration (Real-time Only)
NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# Real-time Configuration
ENABLE_REALTIME=true
REALTIME_PROVIDER=supabase

# Authentication
NEXTAUTH_SECRET=your_nextauth_secret
NEXTAUTH_URL=https://your-domain.vercel.app

# Additional Services (if used)
UPLOADTHING_SECRET=your_uploadthing_secret
UPLOADTHING_APP_ID=your_uploadthing_app_id
```

### Vercel Environment Variables Setup

1. Go to your Vercel project dashboard
2. Navigate to Settings → Environment Variables
3. Add each variable with appropriate values:

| Variable Name | Value | Environment |
|---------------|-------|-------------|
| `NEXT_PUBLIC_APP_URL` | `https://your-domain.vercel.app` | Production |
| `NEXT_PUBLIC_SUPABASE_URL` | `https://your-project-id.supabase.co` | All |
| `NEXT_PUBLIC_SUPABASE_ANON_KEY` | `your_supabase_anon_key` | All |
| `SUPABASE_SERVICE_ROLE_KEY` | `your_supabase_service_role_key` | Production |
| `ENABLE_REALTIME` | `true` | All |
| `REALTIME_PROVIDER` | `supabase` | All |

## 🗄️ Database Setup

### 1. MySQL Database (Primary)

Your existing MySQL database will continue to be the primary data store. No changes needed.

### 2. Supabase Database (Real-time)

Run the following SQL in your Supabase SQL Editor:

```sql
-- Copy and paste the content from supabase-setup.sql
-- This creates the real-time tables and RLS policies
```

## 🚀 Deployment Steps

### 1. Prepare for Deployment

```bash
# Install dependencies
npm install

# Build the project locally to test
npm run build

# Test the production build
npm start
```

### 2. Deploy to Vercel

#### Option A: Vercel CLI

```bash
# Install Vercel CLI
npm i -g vercel

# Login to Vercel
vercel login

# Deploy
vercel --prod
```

#### Option B: GitHub Integration

1. Push your code to GitHub
2. Connect your GitHub repository to Vercel
3. Configure environment variables
4. Deploy automatically on push

### 3. Post-Deployment Setup

1. **Verify Environment Variables**
   ```bash
   # Test environment variables
   npm run test-supabase
   ```

2. **Test Real-time Features**
   ```bash
   # Run comprehensive tests
   npm run test-all
   ```

3. **Monitor Deployment**
   - Check Vercel deployment logs
   - Test real-time messaging
   - Test notifications
   - Verify error handling

## 📊 Monitoring & Health Checks

### Built-in Health Endpoints

The system includes several health check endpoints:

- `/api/health` - Overall system health
- `/api/health/database` - MySQL connection status
- `/api/health/realtime` - Supabase real-time status
- `/api/health/sync` - Hybrid sync service status

### Performance Monitoring

Access performance metrics at:
- `/api/metrics/performance` - Real-time performance data
- `/api/metrics/errors` - Error statistics
- `/api/metrics/connections` - Connection pool status

### Real-time System Status

Check real-time system status:
```bash
# Test real-time connectivity
curl https://your-domain.vercel.app/api/health/realtime

# Get performance metrics
curl https://your-domain.vercel.app/api/metrics/performance
```

## 🔧 Configuration Options

### Real-time Features

You can configure real-time features through environment variables:

```bash
# Enable/disable real-time features
ENABLE_REALTIME=true

# Real-time provider (currently only 'supabase')
REALTIME_PROVIDER=supabase

# Performance tuning
REALTIME_MAX_CONNECTIONS=10
REALTIME_BATCH_SIZE=10
REALTIME_DEBOUNCE_DELAY=300
```

### Fallback Configuration

When real-time features are unavailable, the system automatically falls back to polling:

```bash
# Fallback polling interval (milliseconds)
FALLBACK_POLLING_INTERVAL=5000

# Maximum polling interval (milliseconds)
FALLBACK_MAX_POLLING_INTERVAL=30000
```

## 🛡️ Security Considerations

### Supabase Security

1. **Row Level Security (RLS)** is enabled on all real-time tables
2. **API Keys** are properly scoped (anon key for client, service role for server)
3. **CORS** is configured for your domain only

### Environment Variables

1. Keep `SUPABASE_SERVICE_ROLE_KEY` secret (server-side only)
2. Use `NEXT_PUBLIC_*` prefix only for client-safe variables
3. Rotate keys regularly

## 🔍 Troubleshooting

### Common Issues

1. **Real-time not working**
   - Check Supabase environment variables
   - Verify RLS policies are set up correctly
   - Check browser console for WebSocket errors

2. **High latency**
   - Monitor performance metrics
   - Check connection pool usage
   - Consider increasing batch sizes

3. **Memory issues**
   - Monitor memory usage metrics
   - Check for connection leaks
   - Review error logs

### Debug Commands

```bash
# Test Supabase connection
npm run test-supabase

# Test hybrid sync
npm run test-hybrid-sync

# Test real-time features
npm run test-realtime

# Test error handling
npm run test-error-handling

# Test performance
npm run test-performance

# Run all tests
npm run test-all
```

## 📈 Performance Optimization

### Production Optimizations

1. **Connection Pooling**: Automatically manages Supabase connections
2. **Caching**: TTL-based caching for frequently accessed data
3. **Batching**: Groups operations to reduce API calls
4. **Debouncing**: Prevents excessive real-time updates

### Monitoring Performance

Monitor these key metrics:
- Message latency (target: <100ms)
- Notification latency (target: <100ms)
- Memory usage (target: <50MB)
- Error rate (target: <1%)
- Connection pool utilization

## 🎯 Success Criteria

Your deployment is successful when:

- [x] All environment variables are configured
- [x] MySQL database is accessible
- [x] Supabase real-time tables are created
- [x] Real-time messaging works
- [x] Real-time notifications work
- [x] Error handling and fallbacks work
- [x] Performance metrics are within targets
- [x] All health checks pass

## 📞 Support

If you encounter issues:

1. Check the troubleshooting section above
2. Review Vercel deployment logs
3. Test individual components using the provided test scripts
4. Check Supabase dashboard for real-time connection status

## 🔄 Updates and Maintenance

### Regular Maintenance

1. **Monitor Performance**: Check metrics weekly
2. **Update Dependencies**: Keep packages up to date
3. **Review Logs**: Check for errors and warnings
4. **Test Fallbacks**: Ensure fallback mechanisms work
5. **Cleanup Data**: Old real-time data is automatically cleaned up

### Scaling Considerations

As your application grows:
- Monitor connection pool usage
- Consider increasing batch sizes
- Review caching strategies
- Monitor memory usage patterns
- Consider database indexing optimizations
