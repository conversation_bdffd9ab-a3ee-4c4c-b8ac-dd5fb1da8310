/**
 * Performance Testing Suite
 * Comprehensive performance testing for real-time system
 */

import dotenv from 'dotenv';
import path from 'path';

// Load environment variables first
dotenv.config({ path: path.join(process.cwd(), '.env') });

import { performanceMonitor } from '../lib/supabase/performanceMonitor';
import { connectionManager, isRealtimeEnabled } from '../lib/supabase/client';
import { syncMessage, syncNotification } from '../lib/supabase/syncService';
import { v4 as uuidv4 } from 'uuid';

async function testPerformanceMonitoring() {
  console.log('📊 Testing Performance Monitoring...\n');

  if (!isRealtimeEnabled()) {
    console.log('⚠️  Real-time is disabled. Enable ENABLE_REALTIME=true in .env');
    return false;
  }

  try {
    // Test 1: Initialize Performance Monitor
    console.log('1️⃣ Initializing Performance Monitor...');
    performanceMonitor.startMonitoring();
    
    // Get initial metrics
    const initialMetrics = performanceMonitor.getMetrics();
    console.log('   Initial Metrics:', {
      latency: initialMetrics.messageLatency,
      memory: initialMetrics.memoryUsage,
      operations: initialMetrics.operationsPerSecond,
    });

    // Test 2: Measure Operation Performance
    console.log('\n2️⃣ Testing Operation Performance Measurement...');
    
    const testUserId = 'perf-test-user-' + Date.now();
    
    // Measure message sync performance
    const messageResult = await performanceMonitor.measureOperation(
      'message_sync',
      () => syncMessage({
        id: uuidv4(),
        senderId: 'perf-sender',
        receiverId: testUserId,
        content: 'Performance test message',
        createdAt: new Date(),
      })
    );

    console.log('   Message Sync Performance:', messageResult.success ? '✅ Success' : '❌ Failed');

    // Measure notification sync performance
    const notificationResult = await performanceMonitor.measureOperation(
      'notification_sync',
      () => syncNotification({
        id: uuidv4(),
        recipientId: testUserId,
        type: 'performance_test',
        data: { testType: 'performance' },
        createdAt: new Date(),
      })
    );

    console.log('   Notification Sync Performance:', notificationResult.success ? '✅ Success' : '❌ Failed');

    // Test 3: Cache Performance
    console.log('\n3️⃣ Testing Cache Performance...');
    
    const cacheKey = 'test-cache-key';
    const cacheData = { message: 'cached data', timestamp: Date.now() };
    
    // Set cache
    const cacheStartTime = Date.now();
    performanceMonitor.setCache(cacheKey, cacheData);
    const cacheSetTime = Date.now() - cacheStartTime;
    
    // Get cache
    const getCacheStartTime = Date.now();
    const cachedData = performanceMonitor.getCache(cacheKey);
    const cacheGetTime = Date.now() - getCacheStartTime;
    
    console.log('   Cache Set Time:', `${cacheSetTime}ms`);
    console.log('   Cache Get Time:', `${cacheGetTime}ms`);
    console.log('   Cache Data Match:', cachedData?.message === cacheData.message ? '✅' : '❌');

    // Test 4: Batch Operations
    console.log('\n4️⃣ Testing Batch Operations...');
    
    const batchKey = 'test-batch';
    const batchStartTime = Date.now();
    
    // Add operations to batch
    for (let i = 0; i < 5; i++) {
      performanceMonitor.addToBatch(batchKey, {
        id: uuidv4(),
        operation: `batch-operation-${i}`,
        timestamp: Date.now(),
      });
    }
    
    const batchTime = Date.now() - batchStartTime;
    console.log('   Batch Operations Time:', `${batchTime}ms`);

    // Test 5: Debounce Performance
    console.log('\n5️⃣ Testing Debounce Performance...');
    
    let debounceCallCount = 0;
    const debounceStartTime = Date.now();
    
    // Trigger debounced function multiple times
    for (let i = 0; i < 10; i++) {
      performanceMonitor.debounce('test-debounce', () => {
        debounceCallCount++;
      });
    }
    
    // Wait for debounce to complete
    await new Promise(resolve => setTimeout(resolve, 500));
    
    const debounceTime = Date.now() - debounceStartTime;
    console.log('   Debounce Time:', `${debounceTime}ms`);
    console.log('   Debounce Call Count:', debounceCallCount, '(should be 1)');

    // Test 6: Load Testing
    console.log('\n6️⃣ Testing Load Performance...');
    
    const loadTestStartTime = Date.now();
    const loadTestPromises = [];
    
    // Create 20 concurrent operations
    for (let i = 0; i < 20; i++) {
      const promise = performanceMonitor.measureOperation(
        `load_test_${i}`,
        async () => {
          // Simulate work
          await new Promise(resolve => setTimeout(resolve, Math.random() * 100));
          return `load-test-result-${i}`;
        }
      );
      loadTestPromises.push(promise);
    }
    
    const loadTestResults = await Promise.all(loadTestPromises);
    const loadTestTime = Date.now() - loadTestStartTime;
    
    console.log('   Load Test Time:', `${loadTestTime}ms`);
    console.log('   Load Test Results:', loadTestResults.length, 'operations completed');
    console.log('   Average Time per Operation:', `${(loadTestTime / 20).toFixed(2)}ms`);

    // Test 7: Connection Pool Performance
    console.log('\n7️⃣ Testing Connection Pool Performance...');
    
    const poolStats = connectionManager.getPoolStats();
    console.log('   Pool Statistics:', poolStats);

    // Test 8: Memory Usage Monitoring
    console.log('\n8️⃣ Testing Memory Usage Monitoring...');
    
    // Force garbage collection if available
    if (global.gc) {
      global.gc();
    }
    
    const memoryBefore = process.memoryUsage();
    
    // Create some objects to test memory usage
    const testObjects = [];
    for (let i = 0; i < 1000; i++) {
      testObjects.push({
        id: uuidv4(),
        data: new Array(100).fill(`test-data-${i}`),
        timestamp: Date.now(),
      });
    }
    
    const memoryAfter = process.memoryUsage();
    const memoryDiff = memoryAfter.heapUsed - memoryBefore.heapUsed;
    
    console.log('   Memory Usage Before:', `${(memoryBefore.heapUsed / 1024 / 1024).toFixed(2)}MB`);
    console.log('   Memory Usage After:', `${(memoryAfter.heapUsed / 1024 / 1024).toFixed(2)}MB`);
    console.log('   Memory Difference:', `${(memoryDiff / 1024 / 1024).toFixed(2)}MB`);

    // Clean up test objects
    testObjects.length = 0;

    // Test 9: Performance Report
    console.log('\n9️⃣ Testing Performance Report...');
    
    // Wait for metrics to update
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const performanceReport = performanceMonitor.getPerformanceReport();
    console.log('   Performance Status:', performanceReport.status);
    console.log('   Recommendations:', performanceReport.recommendations);
    console.log('   Current Metrics:', {
      latency: performanceReport.metrics.messageLatency.toFixed(2) + 'ms',
      memory: performanceReport.metrics.memoryUsage.toFixed(2) + 'MB',
      operations: performanceReport.metrics.operationsPerSecond.toFixed(2) + ' ops/sec',
      errors: performanceReport.metrics.errorRate.toFixed(2) + '%',
    });

    // Summary
    const allTestsPassed = messageResult.success && notificationResult.success && 
                          cachedData?.message === cacheData.message && 
                          debounceCallCount === 1 && 
                          loadTestResults.length === 20;

    console.log('\n📋 Performance Test Summary:');
    console.log('   Performance Monitor:', '✅ Working');
    console.log('   Operation Measurement:', messageResult.success && notificationResult.success ? '✅' : '❌');
    console.log('   Cache Performance:', cachedData?.message === cacheData.message ? '✅' : '❌');
    console.log('   Batch Operations:', '✅ Working');
    console.log('   Debounce Function:', debounceCallCount === 1 ? '✅' : '❌');
    console.log('   Load Testing:', loadTestResults.length === 20 ? '✅' : '❌');
    console.log('   Connection Pool:', poolStats.poolSize >= 0 ? '✅' : '❌');
    console.log('   Memory Monitoring:', memoryDiff > 0 ? '✅' : '❌');
    console.log('   Performance Report:', performanceReport.status ? '✅' : '❌');
    console.log('   Overall:', allTestsPassed ? '✅ All tests passed' : '⚠️  Some tests had issues');

    return allTestsPassed;
  } catch (error) {
    console.error('❌ Performance test failed:', error);
    return false;
  }
}

// Stress testing
async function testStressPerformance() {
  console.log('\n🔥 Testing Stress Performance...');
  
  if (!isRealtimeEnabled()) {
    console.log('⚠️  Real-time is disabled. Skipping stress test.');
    return true;
  }

  const stressTestStartTime = Date.now();
  const stressTestPromises = [];
  const testUserId = 'stress-test-user-' + Date.now();

  // Create 50 concurrent operations
  for (let i = 0; i < 50; i++) {
    const messagePromise = performanceMonitor.measureOperation(
      `stress_message_${i}`,
      () => syncMessage({
        id: uuidv4(),
        senderId: `stress-sender-${i}`,
        receiverId: testUserId,
        content: `Stress test message ${i}`,
        createdAt: new Date(),
      })
    );

    const notificationPromise = performanceMonitor.measureOperation(
      `stress_notification_${i}`,
      () => syncNotification({
        id: uuidv4(),
        recipientId: testUserId,
        type: 'stress_test',
        data: { stressIndex: i },
        createdAt: new Date(),
      })
    );

    stressTestPromises.push(messagePromise, notificationPromise);
  }

  try {
    const results = await Promise.all(stressTestPromises);
    const stressTestTime = Date.now() - stressTestStartTime;
    
    const successCount = results.filter(r => r.success).length;
    const totalOperations = results.length;
    
    console.log('   📊 Stress Test Results:');
    console.log(`      Total Operations: ${totalOperations}`);
    console.log(`      Successful: ${successCount}`);
    console.log(`      Failed: ${totalOperations - successCount}`);
    console.log(`      Duration: ${stressTestTime}ms`);
    console.log(`      Average per operation: ${(stressTestTime / totalOperations).toFixed(2)}ms`);
    console.log(`      Operations per second: ${(totalOperations / (stressTestTime / 1000)).toFixed(2)}`);
    
    return successCount === totalOperations;
  } catch (error) {
    console.error('   ❌ Stress test failed:', error);
    return false;
  }
}

// Run all tests
async function runAllTests() {
  console.log('🚀 Starting Performance Tests...\n');
  
  const performanceTestsPass = await testPerformanceMonitoring();
  const stressTestsPass = await testStressPerformance();
  
  const allTestsPass = performanceTestsPass && stressTestsPass;
  
  console.log('\n🏁 Final Results:');
  console.log('   Performance Tests:', performanceTestsPass ? '✅ Passed' : '❌ Failed');
  console.log('   Stress Tests:', stressTestsPass ? '✅ Passed' : '❌ Failed');
  console.log('   Overall:', allTestsPass ? '✅ All tests passed' : '❌ Some tests failed');
  
  console.log('\n🎯 Performance Features:');
  console.log('   ✅ Performance monitoring and metrics');
  console.log('   ✅ Operation latency measurement');
  console.log('   ✅ Memory usage tracking');
  console.log('   ✅ Connection pooling');
  console.log('   ✅ Caching with TTL');
  console.log('   ✅ Batch operations');
  console.log('   ✅ Debouncing');
  console.log('   ✅ Load testing');
  console.log('   ✅ Stress testing');
  
  return allTestsPass;
}

// Run the tests
if (require.main === module) {
  runAllTests()
    .then((success) => {
      process.exit(success ? 0 : 1);
    })
    .catch((error) => {
      console.error('❌ Test suite failed:', error);
      process.exit(1);
    });
}

export { testPerformanceMonitoring, testStressPerformance, runAllTests };
