/**
 * Real-time Error Boundary
 * Catches and handles errors in real-time components
 */

'use client';

import React, { Component, ErrorInfo, ReactNode } from 'react';
import { errorHandler } from '@/lib/supabase/errorHandler';
import { connectionRecovery } from '@/lib/supabase/connectionRecovery';
import { fallbackService } from '@/lib/supabase/fallbackService';
import { Button } from '@/components/ui/Button';
import { 
  ExclamationTriangleIcon, 
  ArrowPathIcon,
  WifiIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  enableFallback?: boolean;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  isRecovering: boolean;
  showDetails: boolean;
}

export class RealtimeErrorBoundary extends Component<Props, State> {
  private retryCount = 0;
  private maxRetries = 3;

  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      isRecovering: false,
      showDetails: false,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    return {
      hasError: true,
      error,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Log error to our error handler
    errorHandler.handleError(error, {
      operation: 'realtime_component_error',
      data: {
        componentStack: errorInfo.componentStack,
        errorBoundary: 'RealtimeErrorBoundary',
      },
      timestamp: new Date(),
    });

    // Call custom error handler if provided
    this.props.onError?.(error, errorInfo);

    // Update state with error info
    this.setState({
      errorInfo,
    });

    // Enable fallback mode if configured
    if (this.props.enableFallback) {
      fallbackService.enterFallbackMode();
    }
  }

  handleRetry = async () => {
    if (this.retryCount >= this.maxRetries) {
      console.warn('Max retry attempts reached');
      return;
    }

    this.setState({ isRecovering: true });
    this.retryCount++;

    try {
      // Wait a bit before retrying
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Try to recover connection
      connectionRecovery.forceReconnection();

      // Wait for potential recovery
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Reset error state
      this.setState({
        hasError: false,
        error: null,
        errorInfo: null,
        isRecovering: false,
      });

      console.log('✅ Error boundary recovery successful');
    } catch (recoveryError) {
      console.error('❌ Error boundary recovery failed:', recoveryError);
      this.setState({ isRecovering: false });
    }
  };

  handleEnableFallback = () => {
    fallbackService.enterFallbackMode();
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
    });
  };

  handleDismiss = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
    });
  };

  toggleDetails = () => {
    this.setState(prev => ({ showDetails: !prev.showDetails }));
  };

  render() {
    if (this.state.hasError) {
      // Custom fallback UI provided
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Default error UI
      return (
        <div className="min-h-[200px] flex items-center justify-center p-6">
          <div className="max-w-md w-full bg-white rounded-xl shadow-lg border border-red-200 p-6">
            {/* Header */}
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-red-100 rounded-lg">
                  <ExclamationTriangleIcon className="h-6 w-6 text-red-600" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">
                    Real-time Error
                  </h3>
                  <p className="text-sm text-gray-600">
                    Something went wrong with real-time features
                  </p>
                </div>
              </div>
              <button
                onClick={this.handleDismiss}
                className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
              >
                <XMarkIcon className="h-5 w-5" />
              </button>
            </div>

            {/* Error Message */}
            <div className="mb-4 p-3 bg-red-50 rounded-lg">
              <p className="text-sm text-red-800">
                {this.state.error?.message || 'An unexpected error occurred'}
              </p>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col space-y-2 mb-4">
              <Button
                onClick={this.handleRetry}
                disabled={this.state.isRecovering || this.retryCount >= this.maxRetries}
                className="w-full"
                variant="primary"
              >
                {this.state.isRecovering ? (
                  <>
                    <ArrowPathIcon className="h-4 w-4 mr-2 animate-spin" />
                    Recovering...
                  </>
                ) : (
                  <>
                    <ArrowPathIcon className="h-4 w-4 mr-2" />
                    Retry ({this.maxRetries - this.retryCount} left)
                  </>
                )}
              </Button>

              {this.props.enableFallback && (
                <Button
                  onClick={this.handleEnableFallback}
                  variant="outline"
                  className="w-full"
                >
                  <WifiIcon className="h-4 w-4 mr-2" />
                  Use Fallback Mode
                </Button>
              )}
            </div>

            {/* Error Details Toggle */}
            <div className="border-t border-gray-200 pt-4">
              <button
                onClick={this.toggleDetails}
                className="text-sm text-gray-500 hover:text-gray-700 transition-colors"
              >
                {this.state.showDetails ? 'Hide' : 'Show'} Error Details
              </button>

              {this.state.showDetails && (
                <div className="mt-3 p-3 bg-gray-50 rounded-lg">
                  <div className="text-xs text-gray-600 space-y-2">
                    <div>
                      <strong>Error:</strong> {this.state.error?.name}
                    </div>
                    <div>
                      <strong>Message:</strong> {this.state.error?.message}
                    </div>
                    {this.state.error?.stack && (
                      <div>
                        <strong>Stack:</strong>
                        <pre className="mt-1 text-xs overflow-x-auto whitespace-pre-wrap">
                          {this.state.error.stack}
                        </pre>
                      </div>
                    )}
                    {this.state.errorInfo?.componentStack && (
                      <div>
                        <strong>Component Stack:</strong>
                        <pre className="mt-1 text-xs overflow-x-auto whitespace-pre-wrap">
                          {this.state.errorInfo.componentStack}
                        </pre>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>

            {/* Help Text */}
            <div className="mt-4 text-xs text-gray-500">
              If this problem persists, try refreshing the page or contact support.
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

// HOC for wrapping components with error boundary
export function withRealtimeErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  options?: {
    fallback?: ReactNode;
    enableFallback?: boolean;
    onError?: (error: Error, errorInfo: ErrorInfo) => void;
  }
) {
  const WrappedComponent = (props: P) => (
    <RealtimeErrorBoundary
      fallback={options?.fallback}
      enableFallback={options?.enableFallback}
      onError={options?.onError}
    >
      <Component {...props} />
    </RealtimeErrorBoundary>
  );

  WrappedComponent.displayName = `withRealtimeErrorBoundary(${Component.displayName || Component.name})`;
  
  return WrappedComponent;
}

// Simplified error boundary for basic use cases
export function RealtimeErrorFallback({ 
  error, 
  resetError 
}: { 
  error: Error; 
  resetError: () => void; 
}) {
  return (
    <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
      <div className="flex items-center space-x-2 mb-2">
        <ExclamationTriangleIcon className="h-5 w-5 text-red-600" />
        <h4 className="text-sm font-medium text-red-800">Real-time Error</h4>
      </div>
      <p className="text-sm text-red-700 mb-3">
        {error.message || 'Something went wrong with real-time features'}
      </p>
      <Button onClick={resetError} size="sm" variant="outline">
        Try Again
      </Button>
    </div>
  );
}
