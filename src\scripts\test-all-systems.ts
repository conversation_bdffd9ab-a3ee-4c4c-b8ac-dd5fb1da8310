/**
 * Comprehensive System Test Suite
 * Tests all components of the hybrid real-time system
 */

import dotenv from 'dotenv';
import path from 'path';

// Load environment variables first
dotenv.config({ path: path.join(process.cwd(), '.env') });

import { testSupabaseConnection } from './simple-supabase-test';
import { runAllTests as testHybridSync } from './test-hybrid-sync';
import { runAllTests as testRealtimeMessaging } from './test-realtime-messaging';
import { runAllTests as testRealtimeNotifications } from './test-realtime-notifications';
import { runAllTests as testFrontendIntegration } from './test-frontend-integration';
import { runAllTests as testErrorHandling } from './test-error-handling';
import { runAllTests as testPerformance } from './test-performance';

interface TestResult {
  name: string;
  passed: boolean;
  duration: number;
  error?: string;
}

async function runTestSuite(
  name: string,
  testFunction: () => Promise<boolean>
): Promise<TestResult> {
  console.log(`\n🧪 Running ${name}...`);
  const startTime = Date.now();
  
  try {
    const passed = await testFunction();
    const duration = Date.now() - startTime;
    
    console.log(`${name}: ${passed ? '✅ PASSED' : '❌ FAILED'} (${duration}ms)`);
    
    return {
      name,
      passed,
      duration,
    };
  } catch (error) {
    const duration = Date.now() - startTime;
    const errorMessage = error instanceof Error ? error.message : String(error);
    
    console.log(`${name}: ❌ FAILED (${duration}ms) - ${errorMessage}`);
    
    return {
      name,
      passed: false,
      duration,
      error: errorMessage,
    };
  }
}

async function runAllSystemTests() {
  console.log('🚀 Starting Comprehensive System Tests...\n');
  console.log('=' .repeat(60));
  console.log('HYBRID MYSQL + SUPABASE REAL-TIME SYSTEM TEST SUITE');
  console.log('=' .repeat(60));

  const overallStartTime = Date.now();
  const testResults: TestResult[] = [];

  // Test 1: Basic Supabase Connection
  testResults.push(await runTestSuite(
    'Supabase Connection Test',
    testSupabaseConnection
  ));

  // Test 2: Hybrid Sync Service
  testResults.push(await runTestSuite(
    'Hybrid Sync Service Test',
    testHybridSync
  ));

  // Test 3: Real-time Messaging
  testResults.push(await runTestSuite(
    'Real-time Messaging Test',
    testRealtimeMessaging
  ));

  // Test 4: Real-time Notifications
  testResults.push(await runTestSuite(
    'Real-time Notifications Test',
    testRealtimeNotifications
  ));

  // Test 5: Frontend Integration
  testResults.push(await runTestSuite(
    'Frontend Integration Test',
    testFrontendIntegration
  ));

  // Test 6: Error Handling & Fallbacks
  testResults.push(await runTestSuite(
    'Error Handling & Fallbacks Test',
    testErrorHandling
  ));

  // Test 7: Performance & Optimization
  testResults.push(await runTestSuite(
    'Performance & Optimization Test',
    testPerformance
  ));

  const overallDuration = Date.now() - overallStartTime;

  // Generate comprehensive report
  console.log('\n' + '=' .repeat(60));
  console.log('COMPREHENSIVE TEST RESULTS');
  console.log('=' .repeat(60));

  const passedTests = testResults.filter(r => r.passed);
  const failedTests = testResults.filter(r => !r.passed);

  console.log(`\n📊 Overall Statistics:`);
  console.log(`   Total Tests: ${testResults.length}`);
  console.log(`   Passed: ${passedTests.length} ✅`);
  console.log(`   Failed: ${failedTests.length} ❌`);
  console.log(`   Success Rate: ${((passedTests.length / testResults.length) * 100).toFixed(1)}%`);
  console.log(`   Total Duration: ${overallDuration}ms (${(overallDuration / 1000).toFixed(2)}s)`);

  console.log(`\n📋 Detailed Results:`);
  testResults.forEach((result, index) => {
    const status = result.passed ? '✅ PASSED' : '❌ FAILED';
    const duration = `${result.duration}ms`;
    console.log(`   ${index + 1}. ${result.name}: ${status} (${duration})`);
    if (result.error) {
      console.log(`      Error: ${result.error}`);
    }
  });

  if (failedTests.length > 0) {
    console.log(`\n❌ Failed Tests:`);
    failedTests.forEach(test => {
      console.log(`   • ${test.name}`);
      if (test.error) {
        console.log(`     Error: ${test.error}`);
      }
    });
  }

  console.log(`\n🎯 System Components Status:`);
  console.log(`   ✅ MySQL Database: Primary data storage`);
  console.log(`   ${testResults[0]?.passed ? '✅' : '❌'} Supabase Connection: Real-time provider`);
  console.log(`   ${testResults[1]?.passed ? '✅' : '❌'} Hybrid Sync Service: Data synchronization`);
  console.log(`   ${testResults[2]?.passed ? '✅' : '❌'} Real-time Messaging: Live messaging`);
  console.log(`   ${testResults[3]?.passed ? '✅' : '❌'} Real-time Notifications: Live notifications`);
  console.log(`   ${testResults[4]?.passed ? '✅' : '❌'} Frontend Integration: UI components`);
  console.log(`   ${testResults[5]?.passed ? '✅' : '❌'} Error Handling: Fallback mechanisms`);
  console.log(`   ${testResults[6]?.passed ? '✅' : '❌'} Performance: Optimization & monitoring`);

  console.log(`\n🔧 Architecture Summary:`);
  console.log(`   📊 Data Flow: MySQL (primary) → Supabase (real-time) → Frontend`);
  console.log(`   🔄 Sync Strategy: Write to MySQL first, async sync to Supabase`);
  console.log(`   🛡️  Fallback: Polling-based fallback when real-time unavailable`);
  console.log(`   ⚡ Performance: Connection pooling, caching, batching`);
  console.log(`   🔐 Security: Row Level Security (RLS) in Supabase`);
  console.log(`   📱 Deployment: Vercel-compatible, no Socket.IO issues`);

  const allTestsPassed = testResults.every(r => r.passed);

  if (allTestsPassed) {
    console.log(`\n🎉 ALL TESTS PASSED! 🎉`);
    console.log(`   The hybrid MySQL + Supabase real-time system is working perfectly!`);
    console.log(`   Ready for production deployment.`);
  } else {
    console.log(`\n⚠️  SOME TESTS FAILED`);
    console.log(`   Please review the failed tests and fix the issues before deployment.`);
    console.log(`   The system may still work with reduced functionality.`);
  }

  console.log(`\n📝 Next Steps:`);
  if (allTestsPassed) {
    console.log(`   1. Deploy to Vercel production environment`);
    console.log(`   2. Configure production Supabase environment variables`);
    console.log(`   3. Set up monitoring and alerting`);
    console.log(`   4. Test with real users`);
    console.log(`   5. Monitor performance and optimize as needed`);
  } else {
    console.log(`   1. Fix failed test issues`);
    console.log(`   2. Re-run tests to ensure all pass`);
    console.log(`   3. Review error logs and debug issues`);
    console.log(`   4. Test individual components separately`);
    console.log(`   5. Consider fallback-only mode if real-time issues persist`);
  }

  console.log('\n' + '=' .repeat(60));

  return allTestsPassed;
}

// Performance benchmark
async function runPerformanceBenchmark() {
  console.log('\n⚡ Running Performance Benchmark...');
  
  const benchmarks = [
    { name: 'Message Sync', target: 100, unit: 'ms' },
    { name: 'Notification Sync', target: 100, unit: 'ms' },
    { name: 'Connection Setup', target: 2000, unit: 'ms' },
    { name: 'Memory Usage', target: 50, unit: 'MB' },
    { name: 'Error Rate', target: 1, unit: '%' },
  ];

  console.log('   Performance Targets:');
  benchmarks.forEach(benchmark => {
    console.log(`     ${benchmark.name}: < ${benchmark.target}${benchmark.unit}`);
  });

  console.log('   📝 Note: Actual performance will be measured during individual tests');
}

// System health check
async function systemHealthCheck() {
  console.log('\n🏥 System Health Check...');
  
  const healthChecks = [
    { component: 'Environment Variables', status: 'checking...' },
    { component: 'MySQL Connection', status: 'checking...' },
    { component: 'Supabase Connection', status: 'checking...' },
    { component: 'Real-time Features', status: 'checking...' },
    { component: 'Error Handling', status: 'checking...' },
  ];

  console.log('   Health Status:');
  healthChecks.forEach(check => {
    console.log(`     ${check.component}: ${check.status}`);
  });

  console.log('   📝 Note: Detailed health status will be checked during tests');
}

// Main execution
async function main() {
  try {
    await systemHealthCheck();
    await runPerformanceBenchmark();
    
    const success = await runAllSystemTests();
    
    process.exit(success ? 0 : 1);
  } catch (error) {
    console.error('\n❌ Test suite execution failed:', error);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

export { runAllSystemTests, runPerformanceBenchmark, systemHealthCheck };
