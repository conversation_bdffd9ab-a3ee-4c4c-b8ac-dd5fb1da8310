/**
 * Performance Monitor
 * Monitors and optimizes real-time system performance
 */

export interface PerformanceMetrics {
  connectionTime: number;
  messageLatency: number;
  notificationLatency: number;
  memoryUsage: number;
  activeConnections: number;
  operationsPerSecond: number;
  errorRate: number;
  lastUpdated: Date;
}

export interface PerformanceConfig {
  maxConnections: number;
  maxMemoryUsage: number; // MB
  maxLatency: number; // ms
  batchSize: number;
  debounceDelay: number; // ms
  cacheTimeout: number; // ms
  enableMetrics: boolean;
}

export class PerformanceMonitor {
  private static instance: PerformanceMonitor;
  private metrics: PerformanceMetrics = {
    connectionTime: 0,
    messageLatency: 0,
    notificationLatency: 0,
    memoryUsage: 0,
    activeConnections: 0,
    operationsPerSecond: 0,
    errorRate: 0,
    lastUpdated: new Date(),
  };

  private config: PerformanceConfig = {
    maxConnections: 10,
    maxMemoryUsage: 100, // 100MB
    maxLatency: 1000, // 1 second
    batchSize: 10,
    debounceDelay: 300,
    cacheTimeout: 60000, // 1 minute
    enableMetrics: true,
  };

  private operationCounts: Map<string, number> = new Map();
  private latencyMeasurements: number[] = [];
  private errorCounts: Map<string, number> = new Map();
  private cache: Map<string, { data: any; timestamp: number }> = new Map();
  private batchQueue: Map<string, any[]> = new Map();
  private debounceTimers: Map<string, NodeJS.Timeout> = new Map();

  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor();
    }
    return PerformanceMonitor.instance;
  }

  /**
   * Start performance monitoring
   */
  startMonitoring(): void {
    if (!this.config.enableMetrics) return;

    console.log('📊 Starting performance monitoring...');

    // Update metrics every 5 seconds
    setInterval(() => {
      this.updateMetrics();
    }, 5000);

    // Clean up cache every minute
    setInterval(() => {
      this.cleanupCache();
    }, 60000);

    // Process batched operations every second
    setInterval(() => {
      this.processBatchedOperations();
    }, 1000);
  }

  /**
   * Measure operation performance
   */
  measureOperation<T>(
    operationName: string,
    operation: () => Promise<T>
  ): Promise<T> {
    const startTime = Date.now();
    
    return operation()
      .then(result => {
        const endTime = Date.now();
        const latency = endTime - startTime;
        
        this.recordLatency(operationName, latency);
        this.incrementOperationCount(operationName);
        
        return result;
      })
      .catch(error => {
        const endTime = Date.now();
        const latency = endTime - startTime;
        
        this.recordLatency(operationName, latency);
        this.incrementErrorCount(operationName);
        
        throw error;
      });
  }

  /**
   * Record latency measurement
   */
  private recordLatency(operation: string, latency: number): void {
    this.latencyMeasurements.push(latency);
    
    // Keep only last 100 measurements
    if (this.latencyMeasurements.length > 100) {
      this.latencyMeasurements = this.latencyMeasurements.slice(-100);
    }

    // Log slow operations
    if (latency > this.config.maxLatency) {
      console.warn(`⚠️  Slow operation detected: ${operation} took ${latency}ms`);
    }
  }

  /**
   * Increment operation count
   */
  private incrementOperationCount(operation: string): void {
    const current = this.operationCounts.get(operation) || 0;
    this.operationCounts.set(operation, current + 1);
  }

  /**
   * Increment error count
   */
  private incrementErrorCount(operation: string): void {
    const current = this.errorCounts.get(operation) || 0;
    this.errorCounts.set(operation, current + 1);
  }

  /**
   * Update performance metrics
   */
  private updateMetrics(): void {
    // Calculate average latency
    if (this.latencyMeasurements.length > 0) {
      const avgLatency = this.latencyMeasurements.reduce((a, b) => a + b, 0) / this.latencyMeasurements.length;
      this.metrics.messageLatency = avgLatency;
      this.metrics.notificationLatency = avgLatency;
    }

    // Calculate operations per second
    const totalOperations = Array.from(this.operationCounts.values()).reduce((a, b) => a + b, 0);
    this.metrics.operationsPerSecond = totalOperations / 5; // 5-second window

    // Calculate error rate
    const totalErrors = Array.from(this.errorCounts.values()).reduce((a, b) => a + b, 0);
    this.metrics.errorRate = totalOperations > 0 ? (totalErrors / totalOperations) * 100 : 0;

    // Get memory usage (if available)
    if (typeof window !== 'undefined' && 'performance' in window && 'memory' in (window.performance as any)) {
      const memory = (window.performance as any).memory;
      this.metrics.memoryUsage = memory.usedJSHeapSize / 1024 / 1024; // Convert to MB
    }

    this.metrics.lastUpdated = new Date();

    // Reset counters for next window
    this.operationCounts.clear();
    this.errorCounts.clear();
  }

  /**
   * Cache data with TTL
   */
  setCache(key: string, data: any): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
    });
  }

  /**
   * Get cached data
   */
  getCache(key: string): any | null {
    const cached = this.cache.get(key);
    if (!cached) return null;

    // Check if cache is expired
    if (Date.now() - cached.timestamp > this.config.cacheTimeout) {
      this.cache.delete(key);
      return null;
    }

    return cached.data;
  }

  /**
   * Clean up expired cache entries
   */
  private cleanupCache(): void {
    const now = Date.now();
    for (const [key, cached] of this.cache.entries()) {
      if (now - cached.timestamp > this.config.cacheTimeout) {
        this.cache.delete(key);
      }
    }
  }

  /**
   * Add operation to batch queue
   */
  addToBatch(batchKey: string, operation: any): void {
    if (!this.batchQueue.has(batchKey)) {
      this.batchQueue.set(batchKey, []);
    }
    
    const queue = this.batchQueue.get(batchKey)!;
    queue.push(operation);

    // Process batch if it reaches max size
    if (queue.length >= this.config.batchSize) {
      this.processBatch(batchKey);
    }
  }

  /**
   * Process a specific batch
   */
  private processBatch(batchKey: string): void {
    const queue = this.batchQueue.get(batchKey);
    if (!queue || queue.length === 0) return;

    console.log(`📦 Processing batch: ${batchKey} (${queue.length} operations)`);

    // Clear the queue
    this.batchQueue.set(batchKey, []);

    // Process operations (implementation depends on batch type)
    // This is a placeholder - actual implementation would depend on the operation type
  }

  /**
   * Process all batched operations
   */
  private processBatchedOperations(): void {
    for (const batchKey of this.batchQueue.keys()) {
      this.processBatch(batchKey);
    }
  }

  /**
   * Debounce function execution
   */
  debounce(key: string, fn: () => void): void {
    // Clear existing timer
    const existingTimer = this.debounceTimers.get(key);
    if (existingTimer) {
      clearTimeout(existingTimer);
    }

    // Set new timer
    const timer = setTimeout(() => {
      fn();
      this.debounceTimers.delete(key);
    }, this.config.debounceDelay);

    this.debounceTimers.set(key, timer);
  }

  /**
   * Get current performance metrics
   */
  getMetrics(): PerformanceMetrics {
    return { ...this.metrics };
  }

  /**
   * Get performance report
   */
  getPerformanceReport(): {
    metrics: PerformanceMetrics;
    recommendations: string[];
    status: 'good' | 'warning' | 'critical';
  } {
    const metrics = this.getMetrics();
    const recommendations: string[] = [];
    let status: 'good' | 'warning' | 'critical' = 'good';

    // Check latency
    if (metrics.messageLatency > this.config.maxLatency) {
      recommendations.push(`High message latency: ${metrics.messageLatency.toFixed(2)}ms`);
      status = 'warning';
    }

    // Check memory usage
    if (metrics.memoryUsage > this.config.maxMemoryUsage) {
      recommendations.push(`High memory usage: ${metrics.memoryUsage.toFixed(2)}MB`);
      status = 'critical';
    }

    // Check error rate
    if (metrics.errorRate > 5) {
      recommendations.push(`High error rate: ${metrics.errorRate.toFixed(2)}%`);
      status = 'critical';
    }

    // Check operations per second
    if (metrics.operationsPerSecond > 100) {
      recommendations.push(`High operation rate: ${metrics.operationsPerSecond.toFixed(2)} ops/sec`);
      status = status === 'critical' ? 'critical' : 'warning';
    }

    if (recommendations.length === 0) {
      recommendations.push('Performance is optimal');
    }

    return {
      metrics,
      recommendations,
      status,
    };
  }

  /**
   * Update configuration
   */
  updateConfig(updates: Partial<PerformanceConfig>): void {
    this.config = { ...this.config, ...updates };
  }

  /**
   * Reset metrics
   */
  resetMetrics(): void {
    this.metrics = {
      connectionTime: 0,
      messageLatency: 0,
      notificationLatency: 0,
      memoryUsage: 0,
      activeConnections: 0,
      operationsPerSecond: 0,
      errorRate: 0,
      lastUpdated: new Date(),
    };
    
    this.latencyMeasurements = [];
    this.operationCounts.clear();
    this.errorCounts.clear();
  }

  /**
   * Cleanup resources
   */
  cleanup(): void {
    this.cache.clear();
    this.batchQueue.clear();
    
    // Clear all debounce timers
    this.debounceTimers.forEach(timer => clearTimeout(timer));
    this.debounceTimers.clear();
  }
}

// Export singleton instance
export const performanceMonitor = PerformanceMonitor.getInstance();
