import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { updatePresence } from "@/lib/supabase/syncService";
import { z } from "zod";

const presenceSchema = z.object({
  status: z.enum(['online', 'offline', 'away']),
  typingIn: z.string().optional(),
});

export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { status, typingIn } = presenceSchema.parse(body);

    // Update presence using the server-side sync service
    await updatePresence(session.user.id, status, typingIn);

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error updating presence:", error);
    return NextResponse.json(
      { error: "Failed to update presence" },
      { status: 500 }
    );
  }
}
